using System;
using Core.CrossCuttingConcerns.Caching;

namespace GymProjectBackend
{
    /// <summary>
    /// Owner cache invalidation test sınıfı
    /// Yeni cache invalidation kurallarını test eder
    /// </summary>
    public class Test_OwnerCacheInvalidation
    {
        public static void TestOwnerCachePatterns()
        {
            Console.WriteLine("=== OWNER CACHE INVALIDATION TEST ===\n");

            // Test CompanyId
            int testCompanyId = 5;

            Console.WriteLine("🔍 OWNER İŞLEM ÖNCESİ:");
            Console.WriteLine("Owner bir OperationClaim ekliyor...\n");

            // Owner için cache pattern'lerini al
            var ownerPatterns = CacheInvalidationConfig.GetCachePatternsForRole("owner", testCompanyId);

            Console.WriteLine("📋 OWNER CACHE INVALIDATION PATTERN'LERİ:");
            foreach (var pattern in ownerPatterns)
            {
                Console.WriteLine($"  ✅ {pattern}");
            }

            Console.WriteLine($"\n📊 TOPLAM PATTERN SAYISI: {ownerPatterns.Length}");

            Console.WriteLine("\n🎯 KARŞILAŞTIRMA:");
            
            // Admin pattern'leri
            var adminPatterns = CacheInvalidationConfig.GetCachePatternsForRole("admin", testCompanyId);
            Console.WriteLine($"Admin pattern sayısı: {adminPatterns.Length}");
            
            // Member pattern'leri  
            var memberPatterns = CacheInvalidationConfig.GetCachePatternsForRole("member", testCompanyId);
            Console.WriteLine($"Member pattern sayısı: {memberPatterns.Length}");

            Console.WriteLine("\n✅ SONUÇ:");
            Console.WriteLine("Owner artık sadece ilgili cache'leri temizliyor!");
            Console.WriteLine("Tüm cache'leri temizleme sorunu çözüldü! 🎉");

            Console.WriteLine("\n=== TEST TAMAMLANDI ===");
        }

        public static void TestEntityBasedInvalidation()
        {
            Console.WriteLine("\n=== ENTITY BAZLI CACHE INVALIDATION TEST ===\n");

            int testCompanyId = 5;

            // OperationClaim entity için pattern'leri test et
            var operationClaimPatterns = CacheInvalidationConfig.GetCachePatternsForEntity("OperationClaim", testCompanyId);
            
            Console.WriteLine("🔧 OPERATIONCLAIM ENTITY PATTERN'LERİ:");
            foreach (var pattern in operationClaimPatterns)
            {
                Console.WriteLine($"  ✅ {pattern}");
            }

            // Member entity için pattern'leri test et
            var memberPatterns = CacheInvalidationConfig.GetCachePatternsForEntity("Member", testCompanyId);
            
            Console.WriteLine("\n👤 MEMBER ENTITY PATTERN'LERİ:");
            foreach (var pattern in memberPatterns)
            {
                Console.WriteLine($"  ✅ {pattern}");
            }

            Console.WriteLine("\n✅ ENTITY BAZLI INVALIDATION ÇALIŞIYOR!");
        }
    }
}

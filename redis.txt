🎯 REDIS CACHE SİSTEMİ - KAPSAMLI PROMPT LİSTESİ (GÜNCELLENMİŞ)
================================================================

PROJE: GymKod Pro - Multi-tenant Spor Salonu Yönetim Sistemi
HEDEF: 1000+ salon ve 100.000+ kullanıcı için Redis Cache altyapısı
YAKLAŞIM: Development → Production → Optimization (adım adım)
TEST STRATEJİSİ: Gerçek API endpoint'leri üzerinde test (güvenlik geçici kaldırılarak)

================================================================
PROMPT 1: Docker + Redis Kurulumu ve Gerçek API Testi
================================================================

ÖNCEKİ PROMPT KONTROLÜ: İlk prompt olduğu için kontrol yok.

BAŞLANGIÇ KONTROLÜ:
- GymProjectBackend projesinin çalışır durumda olduğunu doğrula
- .NET 8 SDK kurulu olduğunu kontrol et
- Visual Studio/VS Code açık ve proje yüklenmiş olduğunu kontrol et
- MemberController ve MemberManager'ı incele

GÜVENLİK AYARLARI (GEÇİCİ - SADECE TEST İÇİN):
1. WebAPI/Controllers/MemberController.cs'den [Authorize] attribute'unu kaldır (satır 17)
2. WebAPI/Controllers/MembershipTypeController.cs'den [Authorize] attribute'unu kaldır (satır 12)
3. Business/Concrete/MemberManager.cs'den GetMemberDetails() metodundaki [SecuredOperation("owner,admin")] kaldır (satır 177)
4. Business/Concrete/MemberManager.cs'den GetAllPaginated() metodundaki [SecuredOperation("owner,admin")] kaldır (satır 65)
5. Business/Concrete/MembershipTypeManager.cs'den GetAll() metodundaki [SecuredOperation("owner,admin")] kaldır (satır 50)
6. **CompanyContext.GetCompanyId() metodunu geçici olarak return 1; yap** (JWT olmadığında -1 döndürüyor)
7. Değişiklikleri kaydet ve projeyi build et
8. ⚠️ NOT: Bu değişiklikler sadece test için, prompt sonunda GERİ EKLENECEKTİR

AMAÇ: Development ortamında Redis'i çalıştırmak ve gerçek API endpoint'i üzerinde test yapmak

SORUN:
- Sistemde cache altyapısı yok, her API çağrısı database'e gidiyor
- Redis kurulumu ve Docker bilgisi eksik
- .NET Redis entegrasyonu yapılmamış
- Gerçek API performance baseline'ı bilinmiyor

İSTEDİĞİMİZ:
- Windows 11'de Docker Desktop kurulumu (adım adım)
- Redis container çalıştırma (persistent volume ile)
- .NET projesine Redis NuGet packages ekleme
- appsettings.json Redis configuration
- Program.cs'e Redis dependency injection
- Temel bağlantı testi (ping/pong)
- RedisInsight GUI kurulumu ve bağlantı
- GERÇEK API TEST: /api/member/getmemberdetails endpoint'i

KAPSAM:
- Docker Desktop kurulum rehberi
- Redis container docker-compose.yml
- StackExchange.Redis NuGet package
- ConnectionMultiplexer configuration
- IDatabase interface kullanımı
- Temel Redis komutları (SET/GET/DEL)
- Connection string yönetimi (dev/staging/prod)

TEST SENARYOSU:
1. Proje çalıştır: dotnet run (localhost:5165)
2. API testleri (CompanyID=1 ile test):
   - GET http://localhost:5165/api/member/getmemberdetails
   - GET http://localhost:5165/api/member/getallpaginated?page=1&size=10
   - GET http://localhost:5165/api/membershipType/getall
3. Response time ölç (cache'siz baseline): ~300-500ms bekleniyor
4. Redis'te cache key'i yok olduğunu doğrula: KEYS gym:*
5. Test sonuçlarını kaydet

⚠️ ÖNEMLİ: CompanyContext Test Sorunu ve Çözümü:
- CompanyContext.GetCompanyId() JWT token olmadığında -1 döndürüyor
- Bu yüzden hiçbir veri gelmiyor (CompanyID filter'ı -1 ile çalışmıyor)
- Test için CompanyContext'i geçici olarak 1 döndürecek şekilde değiştir:

  Core/Utilities/Security/CompanyContext/CompanyContext.cs'de:
  ```csharp
  public int GetCompanyId()
  {
      // TEST İÇİN GEÇİCİ - JWT olmadığında 1 döndür
      return 1; // Normal: JWT'den CompanyId parse et
  }
  ```
- Prompt sonunda orijinal kodu geri yükle

BEKLENEN SONUÇLAR:
- Redis container çalışır durumda
- .NET uygulaması Redis'e bağlanabilir
- RedisInsight'tan verileri görebilirim
- GetMemberDetails API'si çalışır ve baseline performance kaydedilir
- Cache key'leri henüz yok (normal)

PROMPT SONU GÜVENLİK RESTORE:
1. [Authorize] attribute'unu MemberController'a geri ekle (satır 17)
2. [Authorize] attribute'unu MembershipTypeController'a geri ekle (satır 12)
3. [SecuredOperation("owner,admin")] attribute'unu MemberManager.GetMemberDetails()'e geri ekle (satır 177)
4. [SecuredOperation("owner,admin")] attribute'unu MemberManager.GetAllPaginated()'e geri ekle (satır 65)
5. [SecuredOperation("owner,admin")] attribute'unu MembershipTypeManager.GetAll()'e geri ekle (satır 50)
6. **CompanyContext.GetCompanyId() metodunu orijinal haline geri çevir** (JWT'den parse eden kod)
7. Değişiklikleri kaydet ve build et

================================================================
PROMPT 2: Core Cache Service ve Gerçek API Cache Testi
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1):
- Docker Desktop kurulu ve çalışıyor mu?
- Redis container ayakta mı? (docker ps komutu ile kontrol)
- RedisInsight bağlantısı çalışıyor mu?
- .NET projesinde StackExchange.Redis package yüklü mü?
- appsettings.json'da Redis connection string doğru mu?
- Program.cs'te Redis DI registration yapılmış mı?
- Temel Redis ping/pong testi çalışıyor mu?
- GetMemberDetails API baseline performance kaydedildi mi?

EĞER ÖNCEKİ PROMPT EKSİKSE: Önce Prompt 1'i tamamla, sonra devam et.

GÜVENLİK AYARLARI (GEÇİCİ - SADECE TEST İÇİN):
1. [Authorize] ve [SecuredOperation] kaldır (test için)
2. Prompt sonunda geri ekle

AMAÇ: Temiz, SOLID prensiplerine uygun cache service altyapısını kurmak ve gerçek API'de test etmek

SORUN:
- ICacheService interface yok
- Redis operations wrapper yok
- JSON serialization sistemi yok
- Error handling ve fallback mechanism yok
- Gerçek API'de cache performans farkı görülemiyor

İSTEDİĞİMİZ:
- ICacheService interface (Get, Set, Remove, Exists, GetKeys)
- RedisCacheService implementation
- Generic type support (T Get<T>, void Set<T>)
- JSON serialization/deserialization
- Expiry time management
- Error handling ve logging
- Fallback mechanism (Redis fail → direct DB)
- Connection resilience
- GERÇEK TEST: GetMemberDetails() metoduna manuel cache ekleme

KAPSAM:
- Core/CrossCuttingConcerns/Caching/ICacheService.cs
- Core/CrossCuttingConcerns/Caching/RedisCacheService.cs
- Newtonsoft.Json serialization
- Exception handling wrapper
- Logging integration
- Dependency injection registration
- MemberManager.GetMemberDetails() metoduna manuel cache kodu ekleme

TEST SENARYOSU:
1. ICacheService'i implement et
2. MemberManager.GetMemberDetails() metoduna manuel cache logic ekle:
   ```csharp
   public IDataResult<List<MembeFilterDto>> GetMemberDetails()
   {
       var cacheKey = "memberdetails_all";
       var cachedData = _cacheService.Get<List<MembeFilterDto>>(cacheKey);
       
       if (cachedData != null)
           return new SuccessDataResult<List<MembeFilterDto>>(cachedData);
           
       var result = _memberDal.GetMemberDetails();
       _cacheService.Set(cacheKey, result, TimeSpan.FromMinutes(5));
       return new SuccessDataResult<List<MembeFilterDto>>(result);
   }
   ```
3. API testleri (CompanyID=1 ile):
   - İlk çağrı: GET http://localhost:5165/api/member/getmemberdetails (DB'den, yavaş)
   - İkinci çağrı: GET http://localhost:5165/api/member/getmemberdetails (Cache'den, hızlı)
   - Pagination test: GET http://localhost:5165/api/member/getallpaginated?page=1&size=10
   - Normal GET test: GET http://localhost:5165/api/membershipType/getall
4. Redis'te cache key'ini doğrula: KEYS memberdetails_*
5. Performance karşılaştır

BEKLENEN SONUÇLAR:
- Cache service'i DI container'dan alınabilir
- String ve object cache'leyebilir
- Redis down olduğunda exception fırlatmaz
- İlk API çağrısı: ~300-500ms (DB)
- İkinci API çağrısı: ~20-50ms (Cache)
- %80+ performance artışı

================================================================
PROMPT 3: Multi-Tenant Cache Key Strategy ve CompanyID Testi
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-2):
- Redis container ve bağlantı çalışıyor mu?
- ICacheService interface oluşturuldu mu?
- RedisCacheService implementasyonu tamamlandı mı?
- Generic Get<T>/Set<T> method'ları çalışıyor mu?
- JSON serialization/deserialization test edildi mi?
- Error handling ve fallback mechanism var mı?
- DI container'dan ICacheService alınabiliyor mu?
- GetMemberDetails() API'sinde manuel cache çalışıyor mu?
- Performance artışı (%80+) gözlemlendi mi?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

GÜVENLİK AYARLARI (GEÇİCİ - SADECE TEST İÇİN):
1. [Authorize] ve [SecuredOperation] kaldır (test için)
2. CompanyContext'i test için mock'la veya bypass et

AMAÇ: CompanyID bazlı cache isolation ve hierarchical key structure

SORUN:
- Multi-tenant cache key sistemi yok
- CompanyID isolation eksik
- Cache key naming convention belirsiz
- Pattern-based invalidation yok
- Farklı company'ler aynı cache'i kullanıyor (güvenlik riski)

⚠️ ÖNEMLİ: CompanyContext Test Sorunu:
- JWT olmadığında CompanyContext.GetCompanyId() -1 döndürüyor
- Bu yüzden hiçbir veri gelmiyor (CompanyID=-1 filter'ı çalışmıyor)
- Test için CompanyContext'i geçici olarak 1 döndürecek şekilde değiştir

İSTEDİĞİMİZ:
- CacheKeyHelper utility class
- Hierarchical key structure: "gym:{companyId}:{entity}:{id}"
- CompanyContext integration
- Pattern-based key generation
- Bulk invalidation support (company bazlı)
- Key expiration policies (entity bazlı)
- Cache statistics per tenant

KAPSAM:
- Core/CrossCuttingConcerns/Caching/CacheKeyHelper.cs
- CompanyContext integration
- Key pattern examples:
  * "gym:1:member:details" (company 1 member details)
  * "gym:2:member:details" (company 2 member details)
  * "gym:1:payments:2024:*" (yearly payments)
- Bulk operations (GetByPattern, DeleteByPattern)
- Cache namespace management
- MemberManager.GetMemberDetails() metodunu multi-tenant key ile güncelle

TEST SENARYOSU:
1. CacheKeyHelper implement et
2. GetMemberDetails() metodunu güncelle:
   ```csharp
   public IDataResult<List<MembeFilterDto>> GetMemberDetails()
   {
       var companyId = _companyContext.GetCompanyId();
       var cacheKey = CacheKeyHelper.GenerateKey("gym", companyId, "member", "details");
       // gym:1:member:details formatında key oluşur
       
       var cachedData = _cacheService.Get<List<MembeFilterDto>>(cacheKey);
       if (cachedData != null)
           return new SuccessDataResult<List<MembeFilterDto>>(cachedData);
           
       var result = _memberDal.GetMemberDetails();
       _cacheService.Set(cacheKey, result, TimeSpan.FromMinutes(5));
       return new SuccessDataResult<List<MembeFilterDto>>(result);
   }
   ```
3. Multi-tenant test (CompanyID=1 sabit):
   - GET http://localhost:5165/api/member/getmemberdetails
   - GET http://localhost:5165/api/member/getallpaginated?page=1&size=10
   - GET http://localhost:5165/api/membershipType/getall
   - Redis'te gym:1:* pattern'inde key'ler oluştuğunu doğrula
4. Pattern test: KEYS gym:1:* (CompanyID=1 için tüm cache'ler)

BEKLENEN SONUÇLAR:
- Farklı company'lerin cache'leri birbirini etkilemez
- Pattern ile toplu silme çalışır
- Key'ler doğru format'ta oluşur: gym:1:member:details
- Cache isolation sağlanır
- CompanyContext integration çalışır

================================================================
PROMPT 4: AOP Cache Aspect Implementation ve Otomatik Cache
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-3):
- Redis ve Cache Service çalışıyor mu?
- CacheKeyHelper class'ı oluşturuldu mu?
- Multi-tenant key generation ("gym:{companyId}:{entity}:{id}") çalışıyor mu?
- CompanyContext integration yapıldı mı?
- Pattern-based operations (GetByPattern, DeleteByPattern) test edildi mi?
- Bulk invalidation (company bazlı) çalışıyor mu?
- GetMemberDetails() multi-tenant cache çalışıyor mu?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

GÜVENLİK AYARLARI (GEÇİCİ - SADECE TEST İÇİN):
1. [Authorize] ve [SecuredOperation] kaldır (test için)

AMAÇ: Method'lara [CacheAspect] attribute ile otomatik cache ekleme

SORUN:
- Manuel cache kodları her method'da tekrarlanıyor
- AOP cache aspect yok
- Cache invalidation otomatik değil
- Performance monitoring eksik
- GetMemberDetails() metodunda manuel cache kodu var (temizlenmeli)

İSTEDİĞİMİZ:
- CacheAspect attribute class
- Castle.DynamicProxy integration
- Method parameter bazlı key generation
- Automatic cache invalidation
- Cache hit/miss logging
- Performance metrics
- Conditional caching (user role bazlı)

KAPSAM:
- Core/Aspects/Autofac/Caching/CacheAspect.cs
- Method signature bazlı key generation
- Parameter serialization
- Cache duration configuration
- Invalidation triggers
- Performance counters
- Autofac interceptor registration

ÖRNEK KULLANIM:
```csharp
[CacheAspect(duration: 300)] // 5 dakika
public IDataResult<List<MembeFilterDto>> GetMemberDetails()

[CacheAspect(duration: 1800, invalidateOn: "Member")]
public IDataResult<Member> GetMemberById(int id)
```

TEST SENARYOSU:
1. CacheAspect attribute'unu implement et
2. GetMemberDetails() metodundan manuel cache kodlarını kaldır
3. [CacheAspect(300)] attribute'unu ekle:
   ```csharp
   [SecuredOperation("owner,admin")] // Test için kaldırılacak
   [CacheAspect(300)] // 5 dakika cache
   public IDataResult<List<MembeFilterDto>> GetMemberDetails()
   {
       return new SuccessDataResult<List<MembeFilterDto>>(_memberDal.GetMemberDetails());
   }
   ```
4. Autofac interceptor'ı kaydet
5. API test et:
   - İlk çağrı: Aspect cache'e yükler
   - İkinci çağrı: Aspect cache'den döner
6. Cache key'ini kontrol et (aspect tarafından oluşturulan)

BEKLENEN SONUÇLAR:
- Method'lara attribute eklediğimde otomatik cache çalışır
- Aynı parametrelerle ikinci çağrıda cache'den döner
- Manuel cache kodları temizlenir
- AOP pattern ile temiz kod
- Performance monitoring aktif

================================================================
PROMPT 5: Manager Sınıflarına Toplu Cache Entegrasyonu
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-4):
- Redis, Cache Service ve Key Strategy çalışıyor mu?
- CacheAspect attribute class'ı oluşturuldu mu?
- Castle.DynamicProxy integration yapıldı mı?
- Method parameter bazlı key generation çalışıyor mu?
- [CacheAspect] attribute test edildi mi?
- Autofac interceptor registration tamamlandı mı?
- Cache hit/miss logging aktif mi?
- GetMemberDetails() otomatik cache çalışıyor mu?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

GÜVENLİK AYARLARI (GEÇİCİ - SADECE TEST İÇİN):
1. Test edilecek manager metotlarından [SecuredOperation] kaldır
2. İlgili controller'lardan [Authorize] kaldır

AMAÇ: Mevcut Business layer'daki manager'lara cache aspect'leri eklemek

SORUN:
- MemberManager, PaymentManager vb. cache kullanmıyor
- Hot/Warm/Cold data classification yok
- Cache strategy belirsiz
- Pagination cache sistemi yok
- Hangi metotların cache'lenmesi gerektiği belirsiz

İSTEDİĞİMİZ:
- Kritik manager method'larına cache aspect ekleme
- Data classification (Hot: 5dk, Warm: 30dk, Cold: 24sa)
- Pagination cache strategy
- Search result caching
- Related data invalidation
- Frontend'te sık çağrılan metotları önceliklendirme

CACHE STRATEJİSİ:
```csharp
// Hot Data (5 dakika) - Çok sık çağrılan
[CacheAspect(300)]
GetMemberDetails, GetActiveMembers, GetUserProfile

// Warm Data (30 dakika) - Orta sıklıkta çağrılan
[CacheAspect(1800)]
GetAllPaginated, GetMemberPayments, GetCompanySettings

// Cold Data (24 saat) - Az çağrılan, değişmeyen
[CacheAspect(86400)]
GetAllCities, GetMembershipTypes, GetSystemExercises
```

cache sırası:
AdvancedRateLimitManager.cs
AuthManager.cs
CityManager.cs
CompanyAdressManager.cs
CompanyExerciseManager.cs
CompanyManager.cs
CompanyUserManager.cs
DebtPaymentManager.cs
EntryExitHistoryManager.cs
ExerciseCategoryManager.cs
ExpenseManager.cs
FileManager.cs
LicensePackageManager.cs
LicenseTransactionManager.cs
MemberManager.cs
MembershipFreezeHistoryManager.cs
MembershipManager.cs
MembershipTypeManager.cs
MemberWorkoutProgramManager.cs
OperationClaimManager.cs
PaymentManager.cs
ProductManager.cs
ProfileManager.cs
QrCodeEncryptionManager.cs
RemainingDebtManager.cs
SystemExerciseManager.cs
TownManager.cs
TransactionManager.cs
UnifiedCompanyManager.cs
UserCompanyManager.cs
UserDeviceManager.cs
UserLicenseManager.cs
UserManager.cs
UserOperationClaimManager.cs
WorkoutProgramTemplateManager.cs

şu sırayla managerları tek tek inceleyeceğiz daha sonra içindeki metotların üstüne eğer lazımsa cacheaspect atayacağız değilse bi sonraki metoda geçeceğiz. bu sistemle yavaş yavaş ama emin adımlarla managerlardaki metotlara düzgünce cache kuracağız. sistemimi detaylıca incele ve mevcut mimarime en uygun cache yapısını kuralım. ilerde tekrar tekrar cache sistemiyle uğraşmak istemiyorum.
advancedratelimit kısmında geçici cache kurmuştum bunu yeni sisteme entegre edelim istiyorum. cache atanmaması gereken kısımlara cache atamanı istemiyorum sadece gerekli yerlere cache atayalım ki memory şişmesin.
cache remove aspectlerini de eklemeyi unutmayalım. özetle hangi managerı aspectliyorsak o managerdaki metotların hangi apiye ait olduğunu ve frontta çağrılırken ne kadar sık çağrıldığını öğrenelim. öğrendikten sonra da çağrılma durumunu analiz edip cache i ona göre atayalım. 

TEST SENARYOSU:
1. MemberManager'daki diğer metotları incele:
   ```csharp
   [CacheAspect(1800)] // 30 dakika
   public IDataResult<PaginatedResult<Member>> GetAllPaginated(MemberPagingParameters parameters)

   [CacheAspect(300)] // 5 dakika - Dashboard için
   public IDataResult<List<GetActiveMemberDto>> GetActiveMembers()

   [CacheAspect(1800)] // 30 dakika
   public IDataResult<MemberDetailWithHistoryDto> GetMemberDetailById(int memberId)
   ```

2. API testleri (localhost:5165, CompanyID=1):
   - GET http://localhost:5165/api/member/getmemberdetails
   - GET http://localhost:5165/api/member/getallpaginated?page=1&size=10
   - GET http://localhost:5165/api/membershipType/getall

3. Cache invalidation test:
   - Member ekle/güncelle/sil
   - İlgili cache'lerin temizlendiğini doğrula

4. Performance test:
   - Her endpoint için cache'li/cache'siz karşılaştırma

BEKLENEN SONUÇLAR:
- Manager method'ları cache'den veri döner
- Pagination çalışır
- Related data güncellendiğinde cache temizlenir
- Frontend'te %80+ performance artışı
- Memory usage kontrollü artış

================================================================
PROMPT 6: Cache Invalidation ve Admin Panel Backend
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-5):
- Tüm cache altyapısı çalışıyor mu?
- Manager sınıflarında [CacheAspect] eklendi mi?
- Cache hit/miss çalışıyor mu?
- Pagination cache implementasyonu test edildi mi?
- Hot/Warm/Cold data classification uygulandı mı?
- Related data invalidation çalışıyor mu?
- Frontend API'lerde performance artışı gözlemlendi mi?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

AMAÇ: Cache invalidation sistemi ve Frontend cache admin panel için backend API'leri

SORUN:
- Cache invalidation otomatik değil
- Add/Update/Delete işlemlerinde cache temizlenmiyor
- Frontend cache admin service endpoint'leri yok
- Cache statistics API yok
- Cache management operations eksik

İSTEDİĞİMİZ:
- CacheInvalidationAspect attribute
- Add/Update/Delete metotlarına invalidation
- CacheAdminController implementation
- Cache statistics endpoints
- Cache management operations (clear, flush)
- Tenant-based cache info

KAPSAM:
- Core/Aspects/Autofac/Caching/CacheInvalidationAspect.cs
- WebAPI/Controllers/CacheAdminController.cs
- Cache statistics service
- Memory usage monitoring
- Hit/miss ratio calculation
- Pattern-based operations

CACHE INVALIDATION STRATEJİSİ:
```csharp
// Member işlemlerinde ilgili cache'leri temizle
[CacheInvalidationAspect("gym:*:member:*")]
public IResult Add(Member member)

[CacheInvalidationAspect("gym:*:member:*", "gym:*:memberdetails")]
public IResult Update(Member member)

[CacheInvalidationAspect("gym:*:member:*")]
public IResult Delete(int id)
```

ADMIN PANEL ENDPOINTS:
```
GET /api/cacheadmin/statistics
GET /api/cacheadmin/health
GET /api/cacheadmin/keys
GET /api/cacheadmin/keys/pattern/{pattern}
DELETE /api/cacheadmin/clear/tenant/{id}
DELETE /api/cacheadmin/clear/pattern/{pattern}
GET /api/cacheadmin/tenant/{id}/details
POST /api/cacheadmin/warmup
```

TEST SENARYOSU:
1. CacheInvalidationAspect implement et
2. MemberManager Add/Update/Delete metotlarına ekle
3. Test invalidation:
   - GetMemberDetails() çağır (cache'e yükle)
   - Add new member (cache temizlenmeli)
   - GetMemberDetails() çağır (yeni data DB'den)
4. CacheAdminController implement et
5. Admin API'leri test et

BEKLENEN SONUÇLAR:
- CRUD işlemlerinde cache otomatik temizlenir
- Admin panel API'lerden cache bilgilerini alabilir
- Cache management operations çalışır
- Pattern-based cache temizleme çalışır

================================================================
PROMPT 7: Production Deployment ve Windows Server Kurulumu
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-6):
- Cache invalidation sistemi çalışıyor mu?
- CacheAdminController endpoints test edildi mi?
- Cache statistics API'den veri alınabiliyor mu?
- CRUD işlemlerinde cache temizleme çalışıyor mu?
- Admin panel backend hazır mı?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

AMAÇ: Windows Server 2019'da production Redis kurulumu ve monitoring

SORUN:
- Production Redis configuration yok
- Windows Server Docker kurulumu belirsiz
- Monitoring ve alerting yok
- Backup strategy yok
- SSL/TLS güvenlik yok

İSTEDİĞİMİZ:
- Windows Server 2019 Docker Engine kurulumu
- Production Redis configuration
- SSL/TLS encryption
- Persistent volume management
- Auto-restart configuration
- Memory monitoring
- Performance alerting
- Backup automation

KAPSAM:
- Docker Engine kurulum rehberi (Windows Server)
- Production docker-compose.yml
- Redis.conf optimization
- SSL certificate configuration
- Memory management (8GB+ Redis için)
- Log management
- Health monitoring
- Backup scripts

PRODUCTION CONFIGURATION:
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    container_name: gymkod-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - gymkod-network

volumes:
  redis_data:
    driver: local

networks:
  gymkod-network:
    driver: bridge
```

TEST SENARYOSU:
1. Windows Server'da Docker Engine kurulumu
2. Production Redis deployment
3. SSL/TLS configuration
4. Backup script test
5. Auto-restart test
6. Memory monitoring setup
7. .NET app production Redis connection test

BEKLENEN SONUÇLAR:
- Production server'da Redis çalışır
- SSL/TLS encryption aktif
- Monitoring aktif
- Backup'lar otomatik alınır
- Auto-restart çalışır
- Memory usage optimize

================================================================
PROMPT 8: Performance Optimization ve Load Testing
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-7):
- Production Redis kurulumu tamamlandı mı?
- Windows Server'da Docker Engine çalışıyor mu?
- SSL/TLS configuration aktif mi?
- Monitoring ve alerting kuruldu mu?
- Backup automation çalışıyor mu?
- Auto-restart configuration test edildi mi?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

AMAÇ: Cache performance'ını optimize etmek ve 100K+ kullanıcı için load testing

SORUN:
- Performance bottleneck'ler belirsiz
- Memory optimization yok
- Connection pooling optimization eksik
- 100K+ kullanıcı simülasyonu yapılmamış
- Troubleshooting guide yok

İSTEDİĞİMİZ:
- Performance profiling tools
- Memory usage optimization
- Connection pooling tuning
- Cache hit ratio optimization
- Load testing (100K+ concurrent users)
- Troubleshooting playbook
- Performance benchmarks

KAPSAM:
- Redis connection pooling optimization
- Memory eviction policies
- Cache compression
- Performance monitoring dashboard
- Load testing scenarios (NBomber/Artillery)
- Memory leak detection
- Performance benchmarks

LOAD TESTING SENARYOLARI:
```csharp
// NBomber Load Test
var scenario = Scenario.Create("cache_load_test", async context =>
{
    // 1000 salon, her salon 100 üye simülasyonu
    var companyId = Random.Next(1, 1001);
    var response = await httpClient.GetAsync($"/api/member/getmemberdetails?companyId={companyId}");
    return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
})
.WithLoadSimulations(
    Simulation.InjectPerSec(rate: 1000, during: TimeSpan.FromMinutes(10))
);
```

PERFORMANCE TARGETS:
- Response time: <100ms (cache hit)
- Throughput: 10,000+ req/sec
- Memory usage: <8GB Redis
- Cache hit ratio: >90%
- Error rate: <0.1%

TEST SENARYOSU:
1. Connection pooling optimize et
2. Memory eviction policies ayarla
3. Cache compression implement et
4. Load testing tool kurulumu
5. 100K+ user simulation
6. Performance metrics collection
7. Bottleneck identification

BEKLENEN SONUÇLAR:
- System 100K+ kullanıcı simülasyonunda stable çalışır
- Memory usage kontrol altında
- Performance metrics healthy
- Cache hit ratio >90%
- Response time <100ms

================================================================
PROMPT 9: Cache Reliability ve Circuit Breaker Pattern
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-8):
- Performance optimization tamamlandı mı?
- Load testing (100K+ users) yapıldı mı?
- Performance targets karşılandı mı?
- Troubleshooting guide hazır mı?
- Performance monitoring dashboard aktif mi?
- Memory usage optimize edildi mi?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

AMAÇ: Cache sisteminin güvenilirliğini artırmak ve Redis down durumunda sistem stability

SORUN:
- Redis down olduğunda sistem çöker
- Cache warming yok, restart sonrası yavaş
- Circuit breaker pattern yok
- Fallback mechanism eksik
- Advanced monitoring eksik

İSTEDİĞİMİZ:
- Cache Health Check & Circuit Breaker pattern
- Cache Warming Service (uygulama başlangıcında)
- Advanced Monitoring & Alerting sistemi
- Cache Compression (memory optimization)
- Fallback mechanism (Redis → Memory Cache → Direct DB)

KAPSAM:
- CacheHealthCheck service
- BackgroundService cache warming
- Circuit breaker implementation (Polly)
- GZIP compression for large objects
- Memory cache fallback
- Advanced alerting rules

CIRCUIT BREAKER PATTERN:
```csharp
public class ResilientCacheService : ICacheService
{
    private readonly ICacheService _primaryCache; // Redis
    private readonly IMemoryCache _fallbackCache; // Memory
    private readonly CircuitBreakerPolicy _circuitBreaker;

    public T Get<T>(string key)
    {
        return _circuitBreaker.Execute(() =>
        {
            return _primaryCache.Get<T>(key);
        });
    }

    // Fallback to memory cache if Redis fails
    private T FallbackGet<T>(string key)
    {
        return _fallbackCache.Get<T>(key);
    }
}
```

CACHE WARMING STRATEJİSİ:
```csharp
public class CacheWarmupService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Uygulama başlangıcında kritik cache'leri yükle
        await WarmupMemberDetails();
        await WarmupActiveMembers();
        await WarmupCompanySettings();
    }
}
```

TEST SENARYOSU:
1. Circuit breaker implement et
2. Cache warming service ekle
3. Redis down simulation:
   - Redis container'ı durdur
   - API'lerin çalışmaya devam ettiğini doğrula
   - Memory cache fallback test et
4. Redis restart simulation:
   - Cache warming'in çalıştığını doğrula

BEKLENEN SONUÇLAR:
- Redis down olduğunda sistem çalışmaya devam eder
- Restart sonrası cache hazır (warming)
- Memory usage optimize
- Circuit breaker pattern çalışır
- Fallback mechanism aktif

================================================================
PROMPT 10: Cache Security ve Production Hardening
================================================================

ÖNCEKİ PROMPT KONTROLÜ (PROMPT 1-9):
- Cache Health Check & Circuit Breaker çalışıyor mu?
- Cache Warming Service aktif mi?
- Redis down simulation başarılı mı?
- Fallback mechanism test edildi mi?
- Advanced monitoring kuruldu mu?
- Performance metrics collection çalışıyor mu?

EĞER ÖNCEKİ PROMPTLAR EKSİKSE: Önce eksikleri tamamla, sonra devam et.

AMAÇ: Cache güvenliğini sağlamak ve production hardening

SORUN:
- Hassas veriler cache'de açık
- Cache tagging sistemi yok
- Smart preloading yok
- Runtime configuration eksik
- Security best practices uygulanmamış

İSTEDİĞİMİZ:
- Data Encryption (hassas veriler için)
- Cache Tagging System (related data invalidation)
- Smart Preloading (popüler verileri önceden yükle)
- Runtime Configuration Management
- Advanced invalidation strategies
- Security hardening

KAPSAM:
- AES encryption for sensitive data
- Tag-based cache invalidation
- Predictive cache loading
- Configuration hot-reload
- Advanced cache patterns
- Security best practices

DATA ENCRYPTION:
```csharp
[CacheAspect(300, encrypt: true)] // Hassas veriler için
public IDataResult<UserProfileDto> GetUserProfile(int userId)

[CacheAspect(1800, tags: ["member", "payment"])] // Tag-based invalidation
public IDataResult<MemberPaymentDto> GetMemberPayments(int memberId)
```

SMART PRELOADING:
```csharp
public class SmartCachePreloader : BackgroundService
{
    // Popüler verileri usage pattern'ine göre önceden yükle
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var popularCompanies = await GetMostActiveCompanies();
        foreach (var company in popularCompanies)
        {
            await PreloadCompanyData(company.Id);
        }
    }
}
```

SECURITY HARDENING:
- Redis AUTH password
- SSL/TLS encryption
- Network isolation
- Access control lists
- Audit logging

TEST SENARYOSU:
1. Data encryption implement et
2. Tag-based invalidation test et
3. Smart preloading configure et
4. Security hardening apply et
5. Runtime configuration test et
6. Final security audit

BEKLENEN SONUÇLAR:
- Hassas veriler encrypt
- Tag-based invalidation çalışır
- Popüler veriler önceden yüklü
- Configuration runtime'da değiştirilebilir
- Security best practices uygulanmış
- Production-ready sistem

================================================================
FINAL TEST VE DEPLOYMENT CHECKLİST
================================================================

✅ CACHE SİSTEMİ DOĞRULAMA:
- [ ] Redis container production'da çalışıyor
- [ ] Multi-tenant cache isolation aktif
- [ ] AOP cache aspects çalışıyor
- [ ] Cache invalidation otomatik
- [ ] Circuit breaker pattern aktif
- [ ] Cache warming çalışıyor
- [ ] Performance targets karşılanıyor
- [ ] Security hardening tamamlanmış

✅ PERFORMANCE TARGETS:
- [ ] Response time <100ms (cache hit)
- [ ] Throughput >10,000 req/sec
- [ ] Cache hit ratio >90%
- [ ] Memory usage <8GB
- [ ] Error rate <0.1%

✅ SECURITY CHECKLİST:
- [ ] Redis AUTH enabled
- [ ] SSL/TLS encryption
- [ ] Data encryption for sensitive data
- [ ] Network isolation
- [ ] Access control
- [ ] Audit logging

✅ MONITORING & ALERTING:
- [ ] Performance monitoring
- [ ] Memory usage alerts
- [ ] Cache hit ratio monitoring
- [ ] Error rate alerts
- [ ] Health checks

✅ BACKUP & RECOVERY:
- [ ] Automated backups
- [ ] Backup verification
- [ ] Recovery procedures
- [ ] Disaster recovery plan

================================================================
MANAGER CACHE ENTEGRASYONU - DETAYLI PLAN
================================================================

ÖNCELIK SIRASI (Frontend kullanım sıklığına göre):

1. **MemberManager** (En kritik - üye yönetimi)
   - GetMemberDetails() ✅ [CacheAspect(300)] - Dashboard
   - GetAllPaginated() [CacheAspect(1800)] - Üye listesi
   - GetActiveMembers() [CacheAspect(300)] - Dashboard aktif üyeler
   - GetMemberDetailById() [CacheAspect(1800)] - Üye detay sayfası
   - GetMembersByCompany() [CacheAspect(1800)] - Şirket üyeleri

2. **PaymentManager** (Ödeme işlemleri)
   - GetAllPaginated() [CacheAspect(1800)] - Ödeme listesi
   - GetPaymentsByMember() [CacheAspect(1800)] - Üye ödemeleri
   - GetMonthlyPayments() [CacheAspect(300)] - Dashboard aylık ödemeler

3. **MembershipManager** (Üyelik işlemleri)
   - GetAll() [CacheAspect(86400)] - Üyelik tipleri (az değişir)
   - GetByMembershipId() [CacheAspect(1800)] - Üyelik detay
   - GetActiveMemberships() [CacheAspect(300)] - Aktif üyelikler

4. **UserManager** (Kullanıcı yönetimi)
   - GetUserProfile() [CacheAspect(300)] - Profil bilgileri
   - GetUsersByCompany() [CacheAspect(1800)] - Şirket kullanıcıları

5. **CompanyManager** (Şirket ayarları)
   - GetCompanySettings() [CacheAspect(86400)] - Ayarlar (az değişir)
   - GetCompanyDetails() [CacheAspect(1800)] - Şirket detayları

CACHE INVALIDATION PATTERN'LERİ:
```csharp
// Member CRUD işlemleri
[CacheInvalidationAspect("gym:*:member:*", "gym:*:memberdetails")]
public IResult AddMember(Member member)

[CacheInvalidationAspect("gym:*:member:*", "gym:*:activemembers")]
public IResult UpdateMember(Member member)

// Payment CRUD işlemleri
[CacheInvalidationAspect("gym:*:payment:*", "gym:*:monthlypayments")]
public IResult AddPayment(Payment payment)

// Membership CRUD işlemleri
[CacheInvalidationAspect("gym:*:membership:*")]
public IResult UpdateMembership(Membership membership)
```

================================================================
PROMPT TAMAMLAMA RAPORU FORMATI
================================================================

Her prompt sonunda aşağıdaki format kullanılacak:

✅ YAPILAN İŞLEMLER RAPORU
🔧 OLUŞTURULAN/DEĞİŞTİRİLEN DOSYALAR
📝 KOD AÇIKLAMALARI
🧪 MANUEL TEST REHBERİ
📚 ÖĞRENME NOKTALARI
⚙️ SİSTEM NASIL ÇALIŞIYOR
🔮 SONRAKI ADIMDA NE YAPACAĞIMIZ

🎯 TEST SONUÇLARI:
- Endpoint 1: GET /api/member/getmemberdetails - İlk: [X]ms, İkinci: [Y]ms, Artış: [Z]%
- Endpoint 2: GET /api/member/getallpaginated - İlk: [X]ms, İkinci: [Y]ms, Artış: [Z]%
- Endpoint 3: GET /api/membershipType/getall - İlk: [X]ms, İkinci: [Y]ms, Artış: [Z]%
- Cache key pattern: gym:1:member:*, gym:1:membershiptype:*
- TTL: [Süre]

🚨 GÜVENLİK RESTORE:
- MemberController [Authorize] geri eklendi (satır 17)
- MembershipTypeController [Authorize] geri eklendi (satır 12)
- MemberManager [SecuredOperation] geri eklendi (satır 65, 177)
- MembershipTypeManager [SecuredOperation] geri eklendi (satır 50)
- **CompanyContext.GetCompanyId() orijinal JWT parse kodu geri yüklendi**
- Test değişiklikleri geri alındı

================================================================
HEDEF: 1000+ salon ve 100.000+ kullanıcının sorunsuz kullanabileceği sistem
================================================================
